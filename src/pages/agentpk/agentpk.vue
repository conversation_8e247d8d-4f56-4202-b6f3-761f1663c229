<template>
  <div class="agentpk-fullscreen">
    <!-- 顶部返回按钮 -->
    <div class="top-bar">
      <button class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>

      <!-- 当有消息时，在顶部显示标题 -->
      <div v-if="chatMessages.length > 0" class="top-title">
        董会答：懂你、懂美团的问答助手
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 聊天内容区域 -->
      <div v-if="chatMessages.length > 0 || isBatchMode" class="chat-content">
        <div ref="scrollWrapper" class="chat-messages" :class="{ 'batch-mode': isBatchMode }">
          <!-- 普通聊天消息列表 -->
          <template v-if="!isBatchMode" v-for="(item, index) in chatMessages" :key="item.key || index">
            <!-- 用户消息和AI回答使用AgentChatItem组件 -->
            <AgentChatItem
              v-if="item.role === 'user' || item.role === 'assistant'"
              :message-data="item"
            />

            <!-- 思考过程组件 -->
            <AgentThinkingProcess
              v-if="item.role === 'thinking'"
              :thinking-data="item.thinkingData"
            />
          </template>

          <!-- 批量测评模式 -->
          <template v-if="isBatchMode">
            <!-- 用户消息 (只显示一次，居中) -->
            <div v-if="chatMessages.length > 0" class="batch-user-message">
              <AgentChatItem
                v-if="chatMessages[chatMessages.length - 1]?.role === 'user'"
                :message-data="chatMessages[chatMessages.length - 1] as IChatStreamContent"
              />
            </div>

            <!-- 三列AI回答 -->
            <div class="batch-responses">
              <!-- 左列 - 真实API -->
              <div class="response-column">
                <AgentResponseWrapper
                  ai-name="董会答 AI"
                  :thinking-data="batchResponses.left.thinking?.thinkingData"
                  :assistant-message="batchResponses.left.assistant"
                />
              </div>

              <!-- 中列 - 模拟AI 1 -->
              <div class="response-column">
                <AgentResponseWrapper
                  ai-name="AI助手 Beta"
                  :thinking-data="batchResponses.center.thinking?.thinkingData"
                  :assistant-message="batchResponses.center.assistant"
                />
              </div>

              <!-- 右列 - 模拟AI 2 -->
              <div class="response-column">
                <AgentResponseWrapper
                  ai-name="智能顾问"
                  :thinking-data="batchResponses.right.thinking?.thinkingData"
                  :assistant-message="batchResponses.right.assistant"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <InputSection
          ref="inputSectionRef"
          :has-messages="chatMessages.length > 0"
          @send-message="handleSendMessage"
          @batch-test="handleBatchTest"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted, ref, nextTick, onBeforeMount } from 'vue';
import { showFailToast } from 'vant';

import { comprehensiveSearchStream } from '@/apis/search';
import { getUserInfo } from '@/apis/common';
import { getUserProfile } from '@/apis/relation';
import InputSection from './components/InputSection.vue';
import AgentChatItem from './components/AgentChatItem.vue';
import AgentThinkingProcess from './components/AgentThinkingProcess.vue';
import AgentResponseWrapper from './components/AgentResponseWrapper.vue';

const router = useRouter();

// 输入框引用
const inputSectionRef = ref<InstanceType<typeof InputSection> | null>(null);

// 滚动容器引用
const scrollWrapper = ref<HTMLElement | null>(null);

// 用户信息
const currentUserId = ref('');
const currentPersonId = ref('');

// 聊天相关状态
const chatMessages = ref<ExtendedChatMessage[]>([]);
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const canSendMessage = ref(true);

// 批量测评相关状态
const isBatchMode = ref(false);
const batchResponses = ref<{
  left: {
    thinking?: ExtendedChatMessage;
    assistant?: IChatStreamContent;
  };
  center: {
    thinking?: ExtendedChatMessage;
    assistant?: IChatStreamContent;
  };
  right: {
    thinking?: ExtendedChatMessage;
    assistant?: IChatStreamContent;
  };
}>({
  left: {},
  center: {},
  right: {}
});

// 定义扩展的消息类型，包含思考过程
type ExtendedChatMessage = IChatStreamContent | {
  role: 'thinking';
  key: string | number;
  thinkingData: {
    items: Array<{
      type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
      message?: string;
      step?: 'start' | 'processing' | 'complete';
      question?: string;
      questions?: string[];
      results?: Array<{ title: string; link: string }>;
    }>;
    isLoading: boolean;
  };
};

// 当前思考过程数据（用于实时更新）
const currentThinkingData = ref<{
  items: Array<{
    type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
    message?: string;
    step?: 'start' | 'processing' | 'complete';
    question?: string;
    questions?: string[];
    results?: Array<{ title: string; link: string }>;
  }>;
  isLoading: boolean;
}>({
  items: [],
  isLoading: false,
});



// 滚动到底部
const scrollToEnd = () => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  }
};

// 处理发送消息
const handleSendMessage = async (message: string) => {
  console.log('🔄 [agentpk] 发送消息:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [agentpk] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  // 如果当前是批量模式，先重置
  if (isBatchMode.value) {
    resetBatchMode();
  }

  await sendSearchMessage(message);
};

// 发送搜索消息
const sendSearchMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [agentpk] 开始发送搜索消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [agentpk] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  canSendMessage.value = false;

  // 禁用输入框
  if (inputSectionRef.value) {
    inputSectionRef.value.setLoading(true);
  }

  // 清空当前思考数据
  currentThinkingData.value.items = [];
  currentThinkingData.value.isLoading = false;

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: messageContent,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 添加思考过程占位符 - 使用特殊的消息类型，每个消息都有独立的思考数据
  const thinkingKey = Date.now() + 1;
  const thinkingMessage: ExtendedChatMessage = {
    role: 'thinking' as const,
    key: thinkingKey,
    thinkingData: {
      items: [] as Array<{
        type: 'status' | 'question' | 'log' | 'search_questions' | 'search_result';
        message?: string;
        step?: 'start' | 'processing' | 'complete';
        question?: string;
        questions?: string[];
        results?: Array<{ title: string; link: string }>;
      }>,
      isLoading: false,
    },
  };
  chatMessages.value.push(thinkingMessage);

  // 保存当前思考消息的引用，用于后续更新
  const currentThinkingMessageRef = thinkingMessage;

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  // 创建新的AbortController
  streamController.value = new AbortController();

  try {
    // 开始综合搜索
    await comprehensiveSearchStream(
      {
        question: messageContent,
        user_id: currentUserId.value,
      },
      {
        onStatus: (data) => {
          console.log('📊 [agentpk] 状态更新:', data);
          // 使用当前思考消息的引用来更新数据
          currentThinkingMessageRef.thinkingData.items.push({
            type: 'status',
            message: data.message,
            step: data.step,
          });
          if (data.step === 'start') {
            currentThinkingMessageRef.thinkingData.isLoading = true;
          } else if (data.step === 'complete') {
            currentThinkingMessageRef.thinkingData.isLoading = false;
          }
        },
        onQuestion: (data) => {
          console.log('❓ [agentpk] 问题:', data);
          currentThinkingMessageRef.thinkingData.items.push({
            type: 'question',
            question: data.question,
          });
        },
        onLog: (data) => {
          console.log('📝 [agentpk] 日志:', data);
          currentThinkingMessageRef.thinkingData.items.push({
            type: 'log',
            message: data.message,
          });
        },
        onKnowledgeContext: (data) => {
          console.log('📚 [agentpk] 知识上下文:', data);
          // 暂时不展示
        },
        onSearchQuestions: (data) => {
          console.log('🔍 [agentpk] 搜索问题:', data);
          currentThinkingMessageRef.thinkingData.items.push({
            type: 'search_questions',
            questions: data.questions,
          });
        },
        onSearchResult: (data) => {
          console.log('📋 [agentpk] 搜索结果:', data);
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          currentThinkingMessageRef.thinkingData.items.push({
            type: 'search_result',
            results,
          });
        },
        onFinalAnswer: (data) => {
          console.log('✅ [agentpk] 最终答案:', data);
          // 完成思考过程
          currentThinkingMessageRef.thinkingData.isLoading = false;

          // 构建完整答案
          const fullAnswer = `${data.core_answer}\n\n${data.detailed_answer}`;

          // 直接更新最后一条助手消息的内容，让AgentChatItem组件处理打字机效果
          if (chatMessages.value.length > 0) {
            const lastMessage = chatMessages.value[chatMessages.value.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.content = fullAnswer;
              lastMessage.isFinish = true;
            }
          }

          // 重新启用输入框
          canSendMessage.value = true;
          streamController.value = null;
          if (inputSectionRef.value) {
            inputSectionRef.value.setLoading(false);
          }
        },
        onError: (error) => {
          console.error('❌ [agentpk] 搜索错误:', error);
          canSendMessage.value = true;
          streamController.value = null;
          if (inputSectionRef.value) {
            inputSectionRef.value.setLoading(false);
          }
        },
        onClose: () => {
          console.log('🏁 [agentpk] 搜索连接关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [agentpk] 发送搜索消息失败:', error);
    canSendMessage.value = true;
    streamController.value = null;
    if (inputSectionRef.value) {
      inputSectionRef.value.setLoading(false);
    }
  }
};

// 处理批量测评
const handleBatchTest = async (message: string) => {
  console.log('🔄 [agentpk] 批量测评:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息
  if (!canSendMessage.value) {
    console.log('🚫 [agentpk] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  // 切换到批量模式
  isBatchMode.value = true;
  canSendMessage.value = false;

  // 禁用输入框
  if (inputSectionRef.value) {
    inputSectionRef.value.setLoading(true);
  }

  // 添加用户消息到chatMessages（用于显示）
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: message,
    key: Date.now(),
    isFinish: true,
    reasoningData: { content: '', status: '' },
  };
  chatMessages.value.push(userMessage);

  // 初始化三个AI的响应数据
  const baseKey = Date.now();

  // 左列 - 真实API调用
  await startLeftColumnResponse(message, baseKey);

  // 中列和右列 - 模拟响应
  startMockResponse('center', message, baseKey + 1000);
  startMockResponse('right', message, baseKey + 2000);
};

// 启动左列真实API响应
const startLeftColumnResponse = async (message: string, baseKey: number) => {
  // 创建思考过程数据
  const thinkingMessage: ExtendedChatMessage = {
    role: 'thinking' as const,
    key: baseKey + 1,
    thinkingData: {
      items: [],
      isLoading: false,
    },
  };

  // 创建助手消息
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: baseKey + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };

  // 保存到批量响应数据
  batchResponses.value.left.thinking = thinkingMessage;
  batchResponses.value.left.assistant = assistantMessage;

  // 创建新的AbortController
  streamController.value = new AbortController();

  try {
    // 调用真实API
    await comprehensiveSearchStream(
      {
        question: message,
        user_id: currentUserId.value,
      },
      {
        onStatus: (data) => {
          thinkingMessage.thinkingData.items.push({
            type: 'status',
            message: data.message,
            step: data.step,
          });
          if (data.step === 'start') {
            thinkingMessage.thinkingData.isLoading = true;
          } else if (data.step === 'complete') {
            thinkingMessage.thinkingData.isLoading = false;
          }
        },
        onQuestion: (data) => {
          thinkingMessage.thinkingData.items.push({
            type: 'question',
            question: data.question,
          });
        },
        onLog: (data) => {
          thinkingMessage.thinkingData.items.push({
            type: 'log',
            message: data.message,
          });
        },
        onKnowledgeContext: () => {
          // 暂时不展示
        },
        onSearchQuestions: (data) => {
          thinkingMessage.thinkingData.items.push({
            type: 'search_questions',
            questions: data.questions,
          });
        },
        onSearchResult: (data) => {
          const results = data.results.map(result => ({
            title: result.title,
            link: result.link,
          }));
          thinkingMessage.thinkingData.items.push({
            type: 'search_result',
            results,
          });
        },
        onFinalAnswer: (data) => {
          // 完成思考过程
          thinkingMessage.thinkingData.isLoading = false;

          // 构建完整答案
          const fullAnswer = `${data.core_answer}\n\n${data.detailed_answer}`;
          assistantMessage.content = fullAnswer;
          assistantMessage.isFinish = true;

          // 检查是否所有响应都完成了
          checkAllResponsesComplete();
        },
        onError: (error) => {
          console.error('❌ [agentpk] 左列搜索错误:', error);
          assistantMessage.content = '抱歉，获取回答时出现错误。';
          assistantMessage.isFinish = true;
          checkAllResponsesComplete();
        },
        onClose: () => {
          console.log('🏁 [agentpk] 左列搜索连接关闭');
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [agentpk] 左列API调用失败:', error);
    assistantMessage.content = '抱歉，获取回答时出现错误。';
    assistantMessage.isFinish = true;
    checkAllResponsesComplete();
  }
};

// 启动模拟响应
const startMockResponse = (column: 'center' | 'right', message: string, baseKey: number) => {
  // 创建思考过程数据
  const thinkingMessage: ExtendedChatMessage = {
    role: 'thinking' as const,
    key: baseKey + 1,
    thinkingData: {
      items: [],
      isLoading: true,
    },
  };

  // 创建助手消息
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: baseKey + 2,
    isFinish: false,
    reasoningData: { content: '', status: '' },
  };

  // 保存到批量响应数据
  batchResponses.value[column].thinking = thinkingMessage;
  batchResponses.value[column].assistant = assistantMessage;

  // 模拟思考过程
  setTimeout(() => {
    thinkingMessage.thinkingData.items.push({
      type: 'status',
      message: '开始分析问题...',
      step: 'start',
    });
  }, 500);

  setTimeout(() => {
    thinkingMessage.thinkingData.items.push({
      type: 'question',
      question: message,
    });
  }, 1000);

  setTimeout(() => {
    thinkingMessage.thinkingData.items.push({
      type: 'log',
      message: '正在搜索相关信息...',
    });
  }, 1500);

  setTimeout(() => {
    thinkingMessage.thinkingData.items.push({
      type: 'search_questions',
      questions: ['相关问题1', '相关问题2', '相关问题3'],
    });
  }, 2000);

  setTimeout(() => {
    thinkingMessage.thinkingData.items.push({
      type: 'search_result',
      results: [
        { title: '模拟搜索结果1', link: '#' },
        { title: '模拟搜索结果2', link: '#' },
      ],
    });
  }, 2500);

  // 模拟最终回答
  setTimeout(() => {
    thinkingMessage.thinkingData.isLoading = false;
    const aiName = column === 'center' ? 'AI助手 Beta' : '智能顾问';
    assistantMessage.content = `这是来自${aiName}的模拟回答。\n\n针对您的问题"${message}"，我正在模拟一个详细的回答过程。这个回答会包含多个段落来展示不同AI的回答风格和内容差异。\n\n请注意，这是一个模拟回答，用于演示批量测评功能的效果。真实的AI回答会根据具体的API接口返回相应的内容。`;
    assistantMessage.isFinish = true;

    checkAllResponsesComplete();
  }, 3000 + Math.random() * 2000); // 随机延迟，模拟不同AI的响应时间
};

// 检查所有响应是否完成
const checkAllResponsesComplete = () => {
  const leftComplete = batchResponses.value.left.assistant?.isFinish;
  const centerComplete = batchResponses.value.center.assistant?.isFinish;
  const rightComplete = batchResponses.value.right.assistant?.isFinish;

  if (leftComplete && centerComplete && rightComplete) {
    // 所有响应都完成了，重新启用输入框
    canSendMessage.value = true;
    streamController.value = null;
    if (inputSectionRef.value) {
      inputSectionRef.value.setLoading(false);
    }
    console.log('✅ [agentpk] 所有批量测评响应完成');
  }
};

// 重置批量模式
const resetBatchMode = () => {
  isBatchMode.value = false;
  batchResponses.value = {
    left: {},
    center: {},
    right: {}
  };
  canSendMessage.value = true;
  if (inputSectionRef.value) {
    inputSectionRef.value.setLoading(false);
  }
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [agentpk] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [agentpk] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [agentpk] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [agentpk] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [agentpk] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 返回首页
const goBack = async () => {
  console.log('🔙 [agentpk] 返回首页');
  await router.push({
    name: 'chat', // 返回到index.vue (chat路由)
  });
};

// 设置全屏样式
const setFullscreenStyles = () => {
  // 添加全屏样式类
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }

  console.log('🎨 [agentpk] 已应用全屏样式');
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }

  console.log('🎨 [agentpk] 已移除全屏样式');
};

// 页面挂载前的初始化
onBeforeMount(async () => {
  // 先加载用户信息
  await loadUserInfo();
});

// 页面挂载时的初始化
onMounted(async () => {
  console.log('🚀 [agentpk] 全屏页面加载完成');
  setFullscreenStyles();

  // 再次加载用户信息（确保用户信息已加载）
  await loadUserInfo();

  // 获取用户的完整 person_id
  if (currentUserId.value && currentUserId.value !== 'unknown_user') {
    try {
      const userProfile = await getUserProfile({
        user_id: currentUserId.value,
      });

      if (userProfile && userProfile.result === 'success' && userProfile.person) {
        currentPersonId.value = userProfile.person.person_id;
        console.log('👤 [agentpk] 用户 person_id 获取成功:', currentPersonId.value);
      } else {
        console.warn('⚠️ [agentpk] 获取用户 person_id 失败:', userProfile);
        currentPersonId.value = currentUserId.value; // 降级使用 user_id
      }
    } catch (profileError) {
      console.error('❌ [agentpk] 获取用户档案失败:', profileError);
      currentPersonId.value = currentUserId.value; // 降级使用 user_id
    }
  }

  // 初始化完成
  console.log('✅ [agentpk] 页面初始化完成');
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，恢复原始样式');
  removeFullscreenStyles();

  // 如果有正在进行的请求，取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置批量模式状态
  resetBatchMode();

  // 清理完成
  console.log('✅ [agentpk] 组件清理完成');
});
</script>

<style lang="scss" scoped>
.agentpk-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: auto;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.top-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(139, 126, 216, 0.1);
  border: 1px solid rgba(139, 126, 216, 0.3);
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(139, 126, 216, 0.2);
    border-color: rgba(139, 126, 216, 0.5);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.main-content {
  position: absolute;
  top: 60px; // 顶部导航栏高度
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  // 批量测评模式下扩展宽度
  &:has(.batch-mode) {
    max-width: 1800px;
  }

  // 当没有聊天内容时，输入框居中在40%位置
  &:not(:has(.chat-content)) {
    justify-content: flex-start;
    padding-top: calc(40vh - 60px); // 40%减去顶部导航栏高度

    .input-section {
      position: static;
      transform: none;
    }
  }

  .chat-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 20px 0;
      scroll-behavior: smooth;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(139, 126, 216, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(139, 126, 216, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: rgba(139, 126, 216, 0.5);
        }
      }

      // 批量测评模式样式
      &.batch-mode {
        max-width: none;
        width: 100%;

        .batch-user-message {
          display: flex;
          justify-content: center;
          margin-bottom: 32px;

          .agent-chat-message {
            max-width: 600px;
          }
        }

        .batch-responses {
          display: flex;
          gap: 24px;
          width: 100%;
          align-items: flex-start;

          .response-column {
            flex: 1;
            min-width: 0; // 防止flex子项溢出
            display: flex;
            flex-direction: column;
          }
        }
      }
    }
  }

  .input-section {
    flex-shrink: 0;
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}
</style>
